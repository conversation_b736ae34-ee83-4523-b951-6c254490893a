/*
 * 测试程序：验证LCD屏幕休眠时DMA传输控制功能
 * 编译命令: gcc -o test_dma_control test_dma_control.c
 */

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/ioctl.h>
#include <linux/fb.h>
#include <string.h>
#include <errno.h>

#define FBDEV_PATH "/dev/fb0"

void test_blank_control() {
    int fd;
    int ret;
    
    printf("=== LCD DMA控制测试程序 ===\n");
    
    // 打开framebuffer设备
    fd = open(FBDEV_PATH, O_RDWR);
    if (fd == -1) {
        perror("无法打开framebuffer设备");
        return;
    }
    
    printf("成功打开framebuffer设备: %s\n", FBDEV_PATH);
    
    // 测试1: 正常显示状态
    printf("\n1. 设置屏幕为正常显示状态...\n");
    ret = ioctl(fd, FBIOBLANK, FB_BLANK_UNBLANK);
    if (ret == -1) {
        perror("设置FB_BLANK_UNBLANK失败");
    } else {
        printf("   ✓ 屏幕已启用，DMA传输应该正在运行\n");
    }
    
    sleep(3); // 等待3秒观察
    
    // 测试2: 屏幕进入休眠状态
    printf("\n2. 设置屏幕进入休眠状态...\n");
    ret = ioctl(fd, FBIOBLANK, FB_BLANK_POWERDOWN);
    if (ret == -1) {
        perror("设置FB_BLANK_POWERDOWN失败");
    } else {
        printf("   ✓ 屏幕已进入休眠，DMA传输应该已停止\n");
    }
    
    sleep(5); // 休眠5秒
    
    // 测试3: 屏幕重新激活
    printf("\n3. 重新激活屏幕...\n");
    ret = ioctl(fd, FBIOBLANK, FB_BLANK_NORMAL);
    if (ret == -1) {
        perror("设置FB_BLANK_NORMAL失败");
    } else {
        printf("   ✓ 屏幕已重新激活，DMA传输应该已恢复\n");
    }
    
    sleep(3); // 等待3秒观察
    
    // 测试4: 再次进入休眠
    printf("\n4. 再次进入休眠状态...\n");
    ret = ioctl(fd, FBIOBLANK, FB_BLANK_POWERDOWN);
    if (ret == -1) {
        perror("设置FB_BLANK_POWERDOWN失败");
    } else {
        printf("   ✓ 屏幕再次进入休眠\n");
    }
    
    sleep(2);
    
    // 测试5: 最终恢复正常
    printf("\n5. 恢复正常显示...\n");
    ret = ioctl(fd, FBIOBLANK, FB_BLANK_UNBLANK);
    if (ret == -1) {
        perror("设置FB_BLANK_UNBLANK失败");
    } else {
        printf("   ✓ 屏幕已恢复正常显示\n");
    }
    
    close(fd);
    printf("\n=== 测试完成 ===\n");
    printf("请检查内核日志 (dmesg) 查看DMA控制的详细信息\n");
}

void show_usage(const char *prog_name) {
    printf("用法: %s [选项]\n", prog_name);
    printf("选项:\n");
    printf("  -h, --help     显示此帮助信息\n");
    printf("  -t, --test     运行DMA控制测试\n");
    printf("  -s, --sleep    使屏幕进入休眠\n");
    printf("  -w, --wake     唤醒屏幕\n");
    printf("  -n, --normal   设置屏幕为正常模式\n");
}

int main(int argc, char *argv[]) {
    if (argc < 2) {
        show_usage(argv[0]);
        return 1;
    }
    
    if (strcmp(argv[1], "-h") == 0 || strcmp(argv[1], "--help") == 0) {
        show_usage(argv[0]);
        return 0;
    }
    
    if (strcmp(argv[1], "-t") == 0 || strcmp(argv[1], "--test") == 0) {
        test_blank_control();
        return 0;
    }
    
    if (strcmp(argv[1], "-s") == 0 || strcmp(argv[1], "--sleep") == 0) {
        int fd = open(FBDEV_PATH, O_RDWR);
        if (fd == -1) {
            perror("无法打开framebuffer设备");
            return 1;
        }
        
        int ret = ioctl(fd, FBIOBLANK, FB_BLANK_POWERDOWN);
        if (ret == -1) {
            perror("设置休眠失败");
            close(fd);
            return 1;
        }
        
        printf("屏幕已进入休眠状态\n");
        close(fd);
        return 0;
    }
    
    if (strcmp(argv[1], "-w") == 0 || strcmp(argv[1], "--wake") == 0) {
        int fd = open(FBDEV_PATH, O_RDWR);
        if (fd == -1) {
            perror("无法打开framebuffer设备");
            return 1;
        }
        
        int ret = ioctl(fd, FBIOBLANK, FB_BLANK_UNBLANK);
        if (ret == -1) {
            perror("唤醒屏幕失败");
            close(fd);
            return 1;
        }
        
        printf("屏幕已唤醒\n");
        close(fd);
        return 0;
    }
    
    if (strcmp(argv[1], "-n") == 0 || strcmp(argv[1], "--normal") == 0) {
        int fd = open(FBDEV_PATH, O_RDWR);
        if (fd == -1) {
            perror("无法打开framebuffer设备");
            return 1;
        }
        
        int ret = ioctl(fd, FBIOBLANK, FB_BLANK_NORMAL);
        if (ret == -1) {
            perror("设置正常模式失败");
            close(fd);
            return 1;
        }
        
        printf("屏幕已设置为正常模式\n");
        close(fd);
        return 0;
    }
    
    printf("未知选项: %s\n", argv[1]);
    show_usage(argv[0]);
    return 1;
}
